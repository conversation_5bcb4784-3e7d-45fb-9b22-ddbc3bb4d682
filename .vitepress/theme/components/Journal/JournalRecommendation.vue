<template>
  <div class="journal-recommendation" v-if="destination">
    <div class="recommendation-header">
      <div class="header-icon">✨</div>
      <h3 class="header-title">发现更多 {{ destination }} 的精彩游记</h3>
      <p class="header-subtitle">看看其他旅行者的真实体验</p>
    </div>

    <div class="recommendation-content">
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner">
          <div class="spinner"></div>
          <span>正在寻找相关游记...</span>
        </div>
      </div>

      <div v-else-if="journals.length === 0" class="empty-state">
        <div class="empty-icon">📖</div>
        <div class="empty-text">暂时没有找到相关游记</div>
        <div class="empty-hint">成为第一个分享 {{ destination }} 游记的人吧！</div>
      </div>

      <div v-else class="journals-container">
        <div class="journals-list">
          <div
            v-for="journal in journals"
            :key="journal.id"
            class="journal-item"
            @click="handleJournalClick(journal)"
          >
            <div class="journal-cover">
              <img
                v-if="journal.cover_image"
                :src="journal.cover_image"
                :alt="journal.title"
                class="cover-image"
              />
              <div v-else class="cover-placeholder">
                <div class="placeholder-icon">📷</div>
              </div>
              <div class="journal-overlay">
                <div class="overlay-content">
                  <div class="journal-title">{{ journal.title }}</div>
                  <div class="journal-meta">
                    <span class="travel-days">{{ journal.travel_days }}天</span>
                    <span class="travel-mode">{{ journal.travel_mode }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="journal-info">
              <div class="author-info">
                <img
                  :src="journal.avatar"
                  :alt="journal.nickname"
                  class="author-avatar"
                />
                <div class="author-details">
                  <div class="author-name">{{ journal.nickname }}</div>
                  <div class="publish-time">{{ formatDate(journal.created_at) }}</div>
                </div>
              </div>

              <div class="journal-stats">
                <div class="stat-item">
                  <el-icon><Star /></el-icon>
                  <span>{{ journal.like_count || 0 }}</span>
                </div>
                <div class="stat-item">
                  <el-icon><View /></el-icon>
                  <span>{{ journal.view_count || 0 }}</span>
                </div>
              </div>
            </div>

            <div class="journal-preview">
              <div class="content-preview">
                {{ getContentPreview(journal.content) }}
              </div>
              <div class="tags" v-if="journal.tags && journal.tags.length > 0">
                <el-tag
                  v-for="tag in journal.tags.slice(0, 2)"
                  :key="tag"
                  size="small"
                  class="tag-item"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <div class="view-more" v-if="hasMore">
          <el-button @click="loadMore" :loading="loadingMore" type="primary" plain>
            查看更多游记
          </el-button>
        </div>
      </div>
    </div>

    <!-- 游记详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="selectedJournal?.title"
      width="80%"
      :close-on-click-modal="true"
    >
      <div v-if="selectedJournal" class="journal-detail">
        <div class="detail-header">
          <div class="travel-info">
            <span class="travel-route">
              <el-icon><LocationFilled /></el-icon>
              {{ selectedJournal.start_location }} → {{ selectedJournal.destination }}
            </span>
            <span class="travel-days">{{ selectedJournal.travel_days }}天</span>
            <span class="travel-mode">{{ selectedJournal.travel_mode }}</span>
            <span class="travel-date">{{ formatDate(selectedJournal.travel_date) }}</span>
          </div>
          <div class="author-info">
            <img
              :src="selectedJournal.avatar"
              :alt="selectedJournal.nickname"
              class="author-avatar"
            />
            <div class="author-details">
              <div class="author-name">{{ selectedJournal.nickname }}</div>
              <div class="publish-time">{{ formatDate(selectedJournal.created_at) }}</div>
            </div>
          </div>
        </div>

        <div class="detail-content">
          <div v-if="selectedJournal.cover_image" class="cover-image">
            <img :src="selectedJournal.cover_image" :alt="selectedJournal.title" />
          </div>
          <div class="content-markdown" v-html="parseMarkdown(selectedJournal.content)"></div>
        </div>

        <div class="detail-actions">
          <el-button @click="handleLike" :type="selectedJournal.is_liked ? 'primary' : 'default'">
            <el-icon><Star /></el-icon>
            {{ selectedJournal.is_liked ? '已点赞' : '点赞' }}
            ({{ selectedJournal.like_count || 0 }})
          </el-button>
          <el-button @click="handleComment">
            <el-icon><ChatLineRound /></el-icon>
            评论 ({{ selectedJournal.comment_count || 0 }})
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Star, View, LocationFilled, ChatLineRound } from '@element-plus/icons-vue'
import { useUserStore } from '../UserCenter/userStore'
import { markdownService } from '../services/TopmeansMarkdownService.js'

const props = defineProps({
  destination: {
    type: String,
    required: true
  },
  maxCount: {
    type: Number,
    default: 6
  }
})

const emit = defineEmits(['journalClick'])

const userStore = useUserStore()
const API_BASE = import.meta.env.VITE_BACKEND_SRV_URL + '/api'

// 状态变量
const loading = ref(false)
const loadingMore = ref(false)
const journals = ref([])
const hasMore = ref(true)
const currentPage = ref(1)
const showDetailDialog = ref(false)
const selectedJournal = ref(null)

// 初始化
onMounted(() => {
  if (props.destination) {
    loadRecommendations()
  }
})

// 加载推荐游记
const loadRecommendations = async (page = 1) => {
  if (!userStore.isLoggedIn) return

  try {
    if (page === 1) {
      loading.value = true
    } else {
      loadingMore.value = true
    }

    const response = await fetch(
      `${API_BASE}/journals/recommendations/${encodeURIComponent(props.destination)}?page=${page}&pageSize=6`,
      {
        headers: {
          'Authorization': `Bearer ${userStore.token}`
        }
      }
    )

    const result = await response.json()

    if (result.success) {
      if (page === 1) {
        journals.value = result.data || []
      } else {
        journals.value.push(...(result.data || []))
      }

      // 检查是否还有更多数据
      hasMore.value = result.data && result.data.length === 6
      currentPage.value = page
    } else {
      if (page === 1) {
        journals.value = []
      }
      hasMore.value = false
    }
  } catch (error) {
    console.error('加载推荐游记失败:', error)
    if (page === 1) {
      journals.value = []
    }
    hasMore.value = false
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 加载更多
const loadMore = () => {
  if (!hasMore.value || loadingMore.value) return
  loadRecommendations(currentPage.value + 1)
}

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

// 获取内容预览
const getContentPreview = (content) => {
  if (!content) return ''

  const plainText = content
    .replace(/!\[.*?\]\(.*?\)/g, '')
    .replace(/\[.*?\]\(.*?\)/g, '')
    .replace(/[#*>`-]/g, '')
    .replace(/\n/g, ' ')
    .trim()

  return plainText.length > 80 ? plainText.substring(0, 80) + '...' : plainText
}

// 解析Markdown
const parseMarkdown = (content) => {
  return markdownService.parseMarkdown(content)
}

// 处理游记点击
const handleJournalClick = async (journal) => {
  try {
    // 获取游记详情
    const response = await fetch(`${API_BASE}/journals/${journal.id}`, {
      headers: userStore.token ? {
        'Authorization': `Bearer ${userStore.token}`
      } : {}
    })

    const result = await response.json()

    if (result.success) {
      selectedJournal.value = result.data
      showDetailDialog.value = true
    } else {
      ElMessage.error(result.message || '加载游记详情失败')
    }
  } catch (error) {
    console.error('获取游记详情失败:', error)
    ElMessage.error('加载失败，请稍后重试')
  }
}

// 处理点赞
const handleLike = async () => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    return
  }

  if (!selectedJournal.value) return

  try {
    const response = await fetch(`${API_BASE}/journals/${selectedJournal.value.id}/like`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userStore.token}`
      }
    })

    const result = await response.json()

    if (result.success) {
      selectedJournal.value.is_liked = result.isLiked
      selectedJournal.value.like_count += result.isLiked ? 1 : -1

      // 更新列表中的数据
      const journalInList = journals.value.find(j => j.id === selectedJournal.value.id)
      if (journalInList) {
        journalInList.is_liked = result.isLiked
        journalInList.like_count = selectedJournal.value.like_count
      }
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('点赞失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 处理评论
const handleComment = () => {
  ElMessage.info('评论功能正在开发中')
}
</script>

<style scoped>
.journal-recommendation {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 24px;
  margin: 24px 0;
  color: white;
}

.recommendation-header {
  text-align: center;
  margin-bottom: 24px;
}

.header-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.header-title {
  font-size: 20px;
  margin: 0 0 8px 0;
  font-weight: 600;
}

.header-subtitle {
  margin: 0;
  opacity: 0.8;
  font-size: 14px;
}

.recommendation-content {
  min-height: 200px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 0;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 18px;
  margin-bottom: 8px;
}

.empty-hint {
  font-size: 14px;
  opacity: 0.8;
}

.journals-container {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
  backdrop-filter: blur(10px);
}

.journals-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.journal-item {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  color: #333;
}

.journal-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.journal-cover {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.journal-item:hover .cover-image {
  transform: scale(1.05);
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-icon {
  font-size: 48px;
  color: #ccc;
}

.journal-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 16px;
}

.overlay-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.journal-title {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.3;
}

.journal-meta {
  display: flex;
  gap: 8px;
  font-size: 12px;
  opacity: 0.9;
}

.travel-days,
.travel-mode {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
}

.journal-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.author-details {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

.publish-time {
  font-size: 11px;
  color: #999;
}

.journal-stats {
  display: flex;
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.journal-preview {
  padding: 12px 16px;
}

.content-preview {
  font-size: 13px;
  line-height: 1.4;
  color: #666;
  margin-bottom: 8px;
}

.tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.tag-item {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
  font-size: 11px;
}

.view-more {
  text-align: center;
  padding: 16px 0;
}

/* 游记详情对话框样式 */
.journal-detail {
  color: #333;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.travel-info {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.travel-route {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 14px;
}

.travel-days,
.travel-mode,
.travel-date {
  font-size: 12px;
  color: #999;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

.detail-content {
  margin-bottom: 16px;
}

.cover-image {
  width: 100%;
  max-height: 300px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 16px;
}

.content-markdown {
  line-height: 1.6;
  color: #333;
}

.detail-actions {
  display: flex;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .journal-recommendation {
    padding: 16px;
    margin: 16px 0;
  }

  .header-title {
    font-size: 18px;
  }

  .journals-list {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .journal-cover {
    height: 120px;
  }

  .detail-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .travel-info {
    flex-direction: column;
    gap: 8px;
  }
}
</style>