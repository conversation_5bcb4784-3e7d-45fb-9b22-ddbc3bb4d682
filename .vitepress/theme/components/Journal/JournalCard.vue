<template>
  <div class="journal-card" @click="handleCardClick">
    <div class="card-header">
      <div class="author-info">
        <img :src="journal.avatar" :alt="journal.nickname" class="author-avatar" />
        <div class="author-details">
          <div class="author-name">{{ journal.nickname }}</div>
          <div class="publish-time">{{ formatDate(journal.created_at) }}</div>
        </div>
      </div>
      <div class="card-actions" v-if="showActions">
        <el-dropdown @command="handleCommand" trigger="click">
          <span class="action-trigger">
            <el-icon><More /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="edit" v-if="isOwner">编辑</el-dropdown-item>
              <el-dropdown-item command="togglePublic" v-if="isOwner">
                {{ journal.is_public ? '设为私密' : '设为公开' }}
              </el-dropdown-item>
              <el-dropdown-item command="delete" v-if="isOwner" divided>删除</el-dropdown-item>
              <el-dropdown-item command="report" v-if="!isOwner">举报</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <div class="card-content">
      <h3 class="journal-title">{{ journal.title }}</h3>
      <div class="travel-info">
        <span class="travel-route">
          <el-icon><LocationFilled /></el-icon>
          {{ journal.start_location }} → {{ journal.destination }}
        </span>
        <span class="travel-days">{{ journal.travel_days }}天</span>
        <span class="travel-mode">{{ journal.travel_mode }}</span>
        <span class="travel-date">{{ formatDate(journal.travel_date) }}</span>
      </div>

      <div class="content-preview" v-if="journal.content">
        {{ getContentPreview(journal.content) }}
      </div>

      <div class="tags" v-if="journal.tags && journal.tags.length > 0">
        <el-tag
          v-for="tag in journal.tags.slice(0, 3)"
          :key="tag"
          size="small"
          class="tag-item"
        >
          {{ tag }}
        </el-tag>
        <span v-if="journal.tags.length > 3" class="more-tags">
          +{{ journal.tags.length - 3 }}
        </span>
      </div>
    </div>

    <div class="card-cover" v-if="journal.cover_image">
      <img :src="journal.cover_image" :alt="journal.title" class="cover-image" />
    </div>

    <div class="card-footer">
      <div class="engagement-stats">
        <button
          class="stat-button"
          :class="{ 'liked': journal.is_liked }"
          @click.stop="handleLike"
          v-if="showLike"
        >
          <el-icon><Star /></el-icon>
          <span>{{ journal.like_count || 0 }}</span>
        </button>
        <button class="stat-button" @click.stop="handleComment">
          <el-icon><ChatLineRound /></el-icon>
          <span>{{ journal.comment_count || 0 }}</span>
        </button>
        <button class="stat-button">
          <el-icon><View /></el-icon>
          <span>{{ journal.view_count || 0 }}</span>
        </button>
      </div>
      <div class="privacy-indicator" v-if="showPrivacy">
        <el-icon v-if="journal.is_public"><View /></el-icon>
        <el-icon v-else><Lock /></el-icon>
        <span>{{ journal.is_public ? '公开' : '私密' }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  More,
  LocationFilled,
  Star,
  ChatLineRound,
  View,
  Lock
} from '@element-plus/icons-vue'
import { useUserStore } from '../UserCenter/userStore'

const props = defineProps({
  journal: {
    type: Object,
    required: true
  },
  showActions: {
    type: Boolean,
    default: true
  },
  showLike: {
    type: Boolean,
    default: true
  },
  showPrivacy: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['like', 'comment', 'edit', 'delete', 'togglePublic', 'click'])

const userStore = useUserStore()
const API_BASE = import.meta.env.VITE_BACKEND_SRV_URL + '/api'

// 计算属性
const isOwner = computed(() => {
  return userStore.userInfo && userStore.userInfo.id === props.journal.user_id
})

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now - date
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days === 0) {
    const hours = Math.floor(diff / (1000 * 60 * 60))
    if (hours === 0) {
      const minutes = Math.floor(diff / (1000 * 60))
      return minutes < 1 ? '刚刚' : `${minutes}分钟前`
    }
    return `${hours}小时前`
  } else if (days === 1) {
    return '昨天'
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }
}

// 获取内容预览
const getContentPreview = (content) => {
  if (!content) return ''

  // 移除Markdown标记
  const plainText = content
    .replace(/!\[.*?\]\(.*?\)/g, '') // 移除图片
    .replace(/\[.*?\]\(.*?\)/g, '') // 移除链接
    .replace(/[#*>`-]/g, '') // 移除Markdown符号
    .replace(/\n/g, ' ') // 换行符转空格
    .trim()

  return plainText.length > 100 ? plainText.substring(0, 100) + '...' : plainText
}

// 处理卡片点击
const handleCardClick = () => {
  emit('click', props.journal)
}

// 处理点赞
const handleLike = async () => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    return
  }

  try {
    const response = await fetch(`${API_BASE}/journals/${props.journal.id}/like`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userStore.token}`
      }
    })

    const result = await response.json()

    if (result.success) {
      emit('like', {
        journalId: props.journal.id,
        isLiked: result.isLiked
      })
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('点赞失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 处理评论
const handleComment = () => {
  emit('comment', props.journal)
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'edit':
      emit('edit', props.journal)
      break
    case 'togglePublic':
      handleTogglePublic()
      break
    case 'delete':
      handleDelete()
      break
    case 'report':
      handleReport()
      break
  }
}

// 切换公开状态
const handleTogglePublic = async () => {
  try {
    const response = await fetch(`${API_BASE}/journals/${props.journal.id}/toggle-public`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${userStore.token}`
      }
    })

    const result = await response.json()

    if (result.success) {
      ElMessage.success(result.message)
      emit('togglePublic', {
        journalId: props.journal.id,
        isPublic: result.isPublic
      })
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('切换公开状态失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 删除游记
const handleDelete = () => {
  ElMessageBox.confirm(
    '确定要删除这篇游记吗？删除后无法恢复。',
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const response = await fetch(`${API_BASE}/journals/${props.journal.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${userStore.token}`
        }
      })

      const result = await response.json()

      if (result.success) {
        ElMessage.success('游记删除成功')
        emit('delete', props.journal.id)
      } else {
        ElMessage.error(result.message || '删除失败')
      }
    } catch (error) {
      console.error('删除游记失败:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  })
}

// 举报游记
const handleReport = () => {
  ElMessage.info('举报功能正在开发中')
}
</script>

<style scoped>
.journal-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  margin-bottom: 16px;
}

.journal-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.author-details {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.publish-time {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.card-actions {
  display: flex;
  align-items: center;
}

.action-trigger {
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-trigger:hover {
  background-color: #f5f5f5;
}

.card-content {
  padding: 16px;
}

.journal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.travel-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.travel-route {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 14px;
}

.travel-days,
.travel-mode,
.travel-date {
  font-size: 12px;
  color: #999;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

.content-preview {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 12px;
}

.tags {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
}

.tag-item {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.more-tags {
  font-size: 12px;
  color: #999;
}

.card-cover {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.journal-card:hover .cover-image {
  transform: scale(1.05);
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.engagement-stats {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-button {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.stat-button:hover {
  background: #f0f0f0;
  color: #333;
}

.stat-button.liked {
  color: #ff6b6b;
}

.stat-button.liked:hover {
  color: #ff5252;
}

.privacy-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .journal-card {
    margin-bottom: 12px;
  }

  .card-header {
    padding: 12px;
  }

  .card-content {
    padding: 12px;
  }

  .journal-title {
    font-size: 16px;
  }

  .travel-info {
    gap: 8px;
  }

  .travel-info span {
    font-size: 11px;
  }

  .content-preview {
    font-size: 13px;
  }

  .card-cover {
    height: 150px;
  }

  .card-footer {
    padding: 8px 12px;
  }

  .engagement-stats {
    gap: 12px;
  }

  .stat-button {
    font-size: 12px;
  }
}
</style>