/**
 * 表单管理器 - 负责表单数据的持久化、验证和管理
 */
export class TopmeansFormManager {
    constructor() {
        this.formDataKey = 'topmeans_form_data';
    }

    /**
     * 保存表单数据到本地存储
     */
    saveFormData(formData) {
        if (typeof window === 'undefined') return;

        try {
            const dataToSave = {
                ...formData,
                timestamp: Date.now() // 添加时间戳，用于数据有效性检查
            };

            localStorage.setItem(this.formDataKey, JSON.stringify(dataToSave));
        } catch (error) {
        }
    }

    /**
     * 从本地存储加载表单数据
     */
    loadFormData() {
        if (typeof window === 'undefined') return null;

        try {
            const savedData = localStorage.getItem(this.formDataKey);
            if (!savedData) {
                return null;
            }

            const formData = JSON.parse(savedData);

            // 检查数据有效性（可选：设置过期时间，比如7天）
            const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
            if (formData.timestamp && (Date.now() - formData.timestamp > maxAge)) {
                this.clearFormData();
                return null;
            }

            return formData;
        } catch (error) {
            // 如果数据损坏，清除它
            this.clearFormData();
            return null;
        }
    }

    /**
     * 清除本地存储的表单数据
     */
    clearFormData() {
        if (typeof window === 'undefined') return;

        try {
            localStorage.removeItem(this.formDataKey);
        } catch (error) {
        }
    }

    /**
     * 获取默认表单数据
     */
    getDefaultFormData() {
        return {
            s_address: null,
            e_address: null,
            startDate: null,
            dates: 3,
            plan_mode: '往返',
            travel_mode: '自驾',
            // 起点详细信息
            s_location: {
                lng: null,        // 经度
                lat: null,        // 纬度
                province: null,   // 省
                city: null,       // 市
                district: null,   // 区/县
                address: null,    // 完整地址
                adcode: null      // 行政区划代码
            },
            // 终点详细信息
            e_location: {
                lng: null,        // 经度
                lat: null,        // 纬度
                province: null,   // 省
                city: null,       // 市
                district: null,   // 区/县
                address: null,    // 完整地址
                adcode: null      // 行政区划代码
            }
        };
    }

    /**
     * 重置表单到默认值
     */
    resetFormData() {
        // 清除本地存储
        this.clearFormData();

        return this.getDefaultFormData();
    }

    /**
     * 验证表单数据
     */
    validateFormData(formData) {
        const {
            s_address,
            e_address,
            startDate,
            dates
        } = formData;

        // 地址有效性验证（兼容空字符串检测）
        const isAddressValid = [s_address, e_address].every(
            addr => typeof addr === 'string' && addr.trim().length >= 2
        );

        // 日期有效性验证
        const isDateValid = startDate !== null;

        if (dates > 7) {
            return {
                isValid: false,
                message: '出于规划耗时考虑，请勿一次性规划超过7天的行程，可以分多次规划'
            };
        }

        if (!isAddressValid) {
            return {
                isValid: false,
                message: '请输入有效的起点和终点地址（至少2个字符）'
            };
        }

        if (!isDateValid) {
            return {
                isValid: false,
                message: '请选择开始日期'
            };
        }

        return {
            isValid: true,
            message: '表单验证通过'
        };
    }

    /**
     * 合并表单数据
     */
    mergeFormData(currentData, savedData) {
        if (!savedData) return currentData;

        return {
            s_address: savedData.s_address !== undefined ? savedData.s_address : currentData.s_address,
            e_address: savedData.e_address !== undefined ? savedData.e_address : currentData.e_address,
            startDate: savedData.startDate !== undefined ? savedData.startDate : currentData.startDate,
            dates: savedData.dates !== undefined ? savedData.dates : currentData.dates,
            plan_mode: savedData.plan_mode !== undefined ? savedData.plan_mode : currentData.plan_mode,
            travel_mode: savedData.travel_mode !== undefined ? savedData.travel_mode : currentData.travel_mode,
            s_location: savedData.s_location !== undefined ? savedData.s_location : currentData.s_location,
            e_location: savedData.e_location !== undefined ? savedData.e_location : currentData.e_location
        };
    }

        /**
     * 检查表单数据是否有变化
     */
    hasFormDataChanged(oldData, newData) {
        const keys = ['s_address', 'e_address', 'startDate', 'dates', 'plan_mode', 'travel_mode'];

        // 检查基本字段
        if (keys.some(key => oldData[key] !== newData[key])) {
            return true;
        }

        // 检查位置信息是否有变化
        if (this.hasLocationChanged(oldData.s_location, newData.s_location) ||
            this.hasLocationChanged(oldData.e_location, newData.e_location)) {
            return true;
        }

        return false;
    }

    /**
     * 检查位置信息是否有变化
     */
    hasLocationChanged(oldLocation, newLocation) {
        if (!oldLocation && !newLocation) return false;
        if (!oldLocation || !newLocation) return true;

        const locationKeys = ['lng', 'lat', 'province', 'city', 'district', 'address', 'adcode'];
        return locationKeys.some(key => oldLocation[key] !== newLocation[key]);
    }

        /**
     * 获取表单数据的摘要信息
     */
    getFormDataSummary(formData) {
        const { s_address, e_address, startDate, dates, plan_mode, travel_mode, s_location, e_location } = formData;

        return {
            route: `${s_address || '未设置'} → ${e_address || '未设置'}`,
            duration: `${dates}天`,
            mode: `${plan_mode} - ${travel_mode}`,
            startDate: startDate || '未设置',
            startLocationInfo: this.getLocationSummary(s_location),
            endLocationInfo: this.getLocationSummary(e_location),
            hasCoordinates: this.hasCompleteCoordinates(s_location, e_location),
            isComplete: !!(s_address && e_address && startDate && dates > 0)
        };
    }

    /**
     * 获取位置信息摘要
     */
    getLocationSummary(location) {
        if (!location) return '位置信息未设置';

        const parts = [];
        if (location.province) parts.push(location.province);
        if (location.city) parts.push(location.city);
        if (location.district) parts.push(location.district);

        const summary = parts.length > 0 ? parts.join(' ') : (location.address || '地址信息不完整');
        const coordinates = (location.lng && location.lat) ? `(${location.lng}, ${location.lat})` : '(坐标未获取)';

        return `${summary} ${coordinates}`;
    }

    /**
     * 检查是否有完整的坐标信息
     */
    hasCompleteCoordinates(startLocation, endLocation) {
        return !!(startLocation?.lng && startLocation?.lat && endLocation?.lng && endLocation?.lat);
    }

    /**
     * 导出表单数据为JSON
     */
    exportFormData(formData) {
        try {
            return JSON.stringify(formData, null, 2);
        } catch (error) {
            return null;
        }
    }

    /**
     * 从JSON导入表单数据
     */
    importFormData(jsonString) {
        try {
            const data = JSON.parse(jsonString);
            const validation = this.validateFormData(data);

            if (validation.isValid) {
                return data;
            } else {
                throw new Error(validation.message);
            }
        } catch (error) {
            throw error;
        }
    }

    /**
     * 设置自定义存储键
     */
    setStorageKey(key) {
        this.formDataKey = key;
    }

    /**
     * 获取当前存储键
     */
    getStorageKey() {
        return this.formDataKey;
    }

    /**
     * 检查本地存储是否可用
     */
    isLocalStorageAvailable() {
        if (typeof window === 'undefined') return false;

        try {
            const test = '__localStorage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * 获取存储的数据大小（字节）
     */
    getStorageSize() {
        if (!this.isLocalStorageAvailable()) return 0;

        try {
            const data = localStorage.getItem(this.formDataKey);
            return data ? new Blob([data]).size : 0;
        } catch (error) {
            return 0;
        }
    }

    /**
     * 清理过期的表单数据
     */
    cleanupExpiredData(maxAge = 7 * 24 * 60 * 60 * 1000) {
        const savedData = this.loadFormData();
        if (savedData && savedData.timestamp) {
            if (Date.now() - savedData.timestamp > maxAge) {
                this.clearFormData();
                return true; // 已清理
            }
        }
        return false; // 无需清理
    }
}

// 创建单例实例
export const formManager = new TopmeansFormManager();
