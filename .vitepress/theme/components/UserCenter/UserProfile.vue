<template>
  <div class="user-profile">
    <!-- 未登录状态提示 -->
    <div v-if="!userStore.isLoggedIn" class="login-prompt">
      <el-alert
        title="请先登录账号"
        type="warning"
        :closable="false"
        :show-icon="false"
      >
        <template #default>
          <el-button type="primary" @click="goToLogin">去登录</el-button>
        </template>
      </el-alert>
    </div>

    <!-- 已登录状态显示内容 -->
    <template v-else>
      <!-- 顶部个人信息卡片 -->
      <div class="profile-card">
        <div class="profile-header">
          <div class="profile-cover">
            <div class="cover-overlay"></div>
          </div>
          <div class="profile-info">
            <div class="avatar-wrapper">
              <img
                :src="userStore.userInfo.avatar || '/images/default-avatar.jpg'"
                :alt="userStore.userInfo.nickname"
                class="avatar"
              >
              <!-- <div class="avatar-actions">
                <el-upload
                  action="#"
                  :show-file-list="false"
                  :auto-upload="false"
                  :on-change="handleAvatarChange"
                  accept="image/jpeg, image/jpg, image/png, image/gif, image/webp"
                >
                  <button class="upload-btn">
                    <el-icon><Camera /></el-icon>
                    更换头像
                  </button>
                </el-upload>
              </div> -->
            </div>
            <div class="user-details">
              <div class="user-name">
                <div class="editable-field" style="position: relative; display: flex; align-items: center;">
                  <el-input
                    v-if="isEditing.nickname"
                    ref="nicknameInput"
                    v-model="editForm.nickname"
                    size="small"
                    style="width: 300px;"
                    @blur="handleFieldBlur('nickname')"
                  />
                  <h1
                    v-else
                    style="flex: 1; cursor: pointer; margin: 0; white-space: nowrap;"
                    @click="startEditing('nickname')"
                  >
                    {{ userStore.userInfo.nickname || '未设置昵称' }}
                  </h1>
                  <el-icon
                    v-show="!isEditing.nickname"
                    class="edit-icon"
                    style="margin-left: 12px; color: #666;"
                    @click="startEditing('nickname')"
                  >
                    <Edit />
                  </el-icon>
                </div>
              </div>
              <div class="user-signature">
                <div class="editable-field" style="position: relative; display: flex; align-items: center;">
                  <el-input
                    v-if="isEditing.signature"
                    ref="signatureInput"
                    v-model="editForm.signature"
                    size="small"
                    style="width: 300px;"
                    @blur="handleFieldBlur('signature')"
                  />
                  <p
                    v-else
                    style="flex: 1; cursor: pointer; margin: 0; white-space: nowrap;"
                    @click="startEditing('signature')"
                  >
                    {{ userStore.userInfo.signature || '这个人很懒，什么都没写~' }}
                  </p>
                  <el-icon
                    v-show="!isEditing.signature"
                    class="edit-icon"
                    style="margin-left: 12px; color: #666;"
                    @click="startEditing('signature')"
                  >
                    <Edit />
                  </el-icon>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="profile-content">
        <el-tabs v-model="activeTab" class="profile-tabs">
          <el-tab-pane label="我的攻略" name="guides">
            <div class="guides-container">
              <!-- 左侧攻略目录 -->
              <div class="guides-sidebar">
                <div class="sidebar-header">
                  <h3>我的攻略</h3>
                  <span class="guide-count">{{ userGuides.length }} 个攻略</span>
                </div>
                <div class="guides-list">
                  <div
                    v-for="(guide, index) in userGuides"
                    :key="guide.createTime"
                    class="guide-item"
                    :class="{ active: selectedGuideIndex === index }"
                    @click="selectGuide(index)"
                  >
                    <div class="guide-item-header">
                      <h4>{{ getGuideTitle(guide) }}</h4>
                      <span class="guide-days">{{ guide.totalDays }}天</span>
                    </div>
                    <div class="guide-item-meta">
                      <span class="guide-date">
                        <el-icon><Calendar /></el-icon>
                        {{ formatDate(guide.createTime) }}
                      </span>
                    </div>
                  </div>
                  <div v-if="userGuides.length === 0" class="empty-guides">
                    <el-empty description="暂无攻略记录">
                      <el-button type="primary" @click="goToHome">去制作攻略</el-button>
                    </el-empty>
                  </div>
                </div>
              </div>

              <!-- 右侧攻略详情 -->
              <div class="guides-content">
                <div v-if="selectedGuide" class="guide-detail">
                  <div class="guide-detail-header">
                    <h2>{{ getGuideTitle(selectedGuide) }}</h2>
                    <div class="guide-detail-meta">
                      <span>创建时间：{{ formatDate(selectedGuide.createTime) }}</span>
                      <span>总天数：{{ selectedGuide.totalDays }}天</span>
                    </div>
                  </div>

                  <!-- 攻略内容展示区域，参考MapContainer的scroll-area -->
                  <div class="scroll-area guide-scroll-container">
                    <div v-if="selectedGuide.plans && selectedGuide.plans.length > 0">
                      <div v-for="(plan, planIndex) in selectedGuide.plans" :key="planIndex">
                        <!-- 攻略内容 -->
                        <div class="answer-area-container">
                          <div class="answer-area" v-html="parseMarkdown(plan.content, selectedGuide)"></div>
                        </div>

                        <hr class="vitepress-divider" v-if="planIndex < selectedGuide.plans.length - 1">
                      </div>
                    </div>
                    <div v-else class="no-plans">
                      <p>该攻略暂无内容</p>
                    </div>
                  </div>
                </div>

                <div v-else class="no-guide-selected">
                  <el-empty description="请选择一个攻略查看详情" />
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="我的游记" name="journals">
            <div class="journals-container">
              <JournalList
                title="我的游记"
                api-url="/journals/my"
                :show-create-button="true"
                :show-actions="true"
                :show-like="false"
                :show-privacy="true"
                :show-public-filter="true"
                empty-text="还没有写过游记，快来分享你的旅行体验吧！"
                @journal-created="handleJournalCreated"
                @journal-updated="handleJournalUpdated"
                @journal-deleted="handleJournalDeleted"
                @journal-click="handleJournalClick"
              />
            </div>
          </el-tab-pane>

          <el-tab-pane label="账号安全" name="security">
            <div class="security-settings">
              <el-card class="security-card">
                <template #header>
                  <div class="card-header" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                    <span>修改密码</span>
                    <el-button
                      type="primary"
                      link
                      size="small"
                      @click="showChangePassword = true"
                    >
                      修改
                    </el-button>
                  </div>
                </template>
                <p>定期修改密码可以保护账号安全</p>
              </el-card>

              <el-card class="security-card">
                <template #header>
                  <div class="card-header" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                    <span>手机绑定</span>
                    <el-button
                      type="primary"
                      link
                      size="small"
                      @click="showBindPhone = true"
                    >
                      {{ userStore.userInfo.phone ? '修改' : '绑定' }}
                    </el-button>
                  </div>
                </template>
                <p>{{ userStore.userInfo.phone || '未绑定手机号' }}</p>
              </el-card>

              <el-card class="security-card">
                <template #header>
                  <div class="card-header" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                    <span>邮箱绑定</span>
                    <el-button
                      type="primary"
                      link
                      size="small"
                      @click="showBindEmail = true"
                      class="action-btn"
                    >
                      {{ userStore.userInfo.email ? '修改' : '绑定' }}
                    </el-button>
                  </div>
                </template>
                <p>{{ userStore.userInfo.email || '未绑定邮箱' }}</p>
              </el-card>

              <el-card class="security-card">
                <template #header>
                  <div class="card-header" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                    <span>登录状态</span>
                    <el-button
                      type="danger"
                      link
                      size="small"
                      class="action-btn"
                      @click="handleLogout"
                    >
                      立即注销
                    </el-button>
                  </div>
                </template>
                <p>当前登录账号：{{ userStore.userInfo.account }}</p>
                <!-- <p>登录有效期至：{{ new Date(userStore.passwordExpire).toLocaleString() }}</p> -->
              </el-card>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </template>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="showChangePassword"
      title="修改密码"
      width="400px"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input
            v-model="passwordForm.currentPassword"
            type="password"
            show-password
            @input="handleInput('currentPassword')"
            @compositionstart="isComposing = true"
            @compositionend="isComposing = false"
          />
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
            @input="handleInput('newPassword')"
            @compositionstart="isComposing = true"
            @compositionend="isComposing = false"
          />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model.trim="passwordForm.confirmPassword"
            type="password"
            show-password
            @input="handleInput('confirmPassword')"
            @compositionstart="isComposing = true"
            @compositionend="isComposing = false"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer" style="display: flex; justify-content: flex-end; gap: 12px;">
          <el-button size="small" @click="showChangePassword = false">取消</el-button>
          <el-button type="primary" size="small" @click="handleChangePassword">确认修改</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, onMounted, computed, watch } from 'vue'
import { useUserStore } from './userStore'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Calendar, Edit } from '@element-plus/icons-vue'
import { useRouter } from 'vitepress'
import { storeToRefs } from 'pinia'
import { markdownService } from '../services/TopmeansMarkdownService.js'
import JournalList from '../Journal/JournalList.vue'

const router = useRouter()
const userStore = useUserStore()
const { isLoggedIn } = storeToRefs(userStore)
const activeTab = ref('guides')
const showChangePassword = ref(false)
const showBindPhone = ref(false)
const showBindEmail = ref(false)
const API_BASE = `${import.meta.env.VITE_BACKEND_SRV_URL}/api`;

// 编辑状态管理
const showEdit = reactive({
  nickname: false,
  signature: false
})

const isEditing = reactive({
  nickname: false,
  signature: false
})

// 输入框引用
const nicknameInput = ref(null)
const signatureInput = ref(null)

const editForm = reactive({
  nickname: '',
  signature: ''
})

// 初始化编辑表单
const initEditForm = () => {
  editForm.nickname = userStore.userInfo.nickname || ''
  editForm.signature = userStore.userInfo.signature || ''
}
initEditForm()

// 开始编辑
const startEditing = async (field) => {
  isEditing[field] = true
  await nextTick()
  try {
    if (field === 'nickname' && nicknameInput.value) {
      nicknameInput.value.focus()
    } else if (field === 'signature' && signatureInput.value) {
      signatureInput.value.focus()
    }
  } catch (error) {
    console.warn('无法聚焦到输入框:', error)
  }
}

// 字段失焦处理
const handleFieldBlur = async (field) => {
  isEditing[field] = false
  if (editForm[field] !== userStore.userInfo[field]) {
    try {
      await userStore.updateProfile({ [field]: editForm[field] })
      ElMessage.success({
        message: '更新成功',
        duration: 1000
      })
      userStore.userInfo[field] = editForm[field]
    } catch (error) {
      ElMessage.error({
        message: '更新失败',
        duration: 1000
      })
      editForm[field] = userStore.userInfo[field]
    }
  }
}

// 用户信息
const userInfo = ref({
  avatar: '',
  isVerified: false,
  guideCount: 0,
  followers: 0,
  following: 0,
  phone: '',
  email: '',
})

// 密码表单
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const isComposing = ref(false)
const passwordFormRef = ref(null)

// 输入实时处理
const handleInput = (field) => {
  if (!isComposing.value) {
    // 移除trim()保留原始输入
    passwordFormRef.value.validateField(field)
    // 当修改新密码时自动验证确认密码
    if (field === 'newPassword') {
      passwordFormRef.value.validateField('confirmPassword')
    }
  }
}

// 密码验证规则
const passwordRules = reactive({
  currentPassword: [
    { required: true, message: '当前密码不能为空', trigger: ['blur', 'change'] }
  ],
  newPassword: [
    {
      required: true,
      pattern: /^(?=.*[a-zA-Z])(?=.*\d)[\x21-\x7e]{6,20}$/,
      message: '需6-20位, 必须包含字母和数字',
      trigger: ['blur', 'input']
    },
    {
      validator: (rule, value, callback) => {
        if (value === passwordForm.value.currentPassword) {
          callback(new Error('新密码不能与当前密码相同'))
        } else {
          callback()
        }
      },
      trigger: ['input', 'blur']
    }
  ],
  confirmPassword: [
    {
      validator: (rule, value, callback) => {
        if (!value || !passwordForm.value.newPassword) {
          return callback()
        }
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: ['input', 'blur']
    }
  ]
})

// 用户攻略列表
const userGuides = ref([])
const selectedGuideIndex = ref(-1)
const selectedGuide = computed(() => {
  return selectedGuideIndex.value >= 0 ? userGuides.value[selectedGuideIndex.value] : null
})

// 获取用户攻略
const loadUserGuides = async () => {
  try {
    if (!userStore.userInfo?.account) {
      return
    }

    const guides = await userStore.getUserGuides()
    userGuides.value = guides

    // 如果有攻略，默认选择第一个
    if (guides.length > 0) {
      selectedGuideIndex.value = 0
    }
  } catch (error) {
    ElMessage.error({
      message: '获取攻略失败: ' + error.message,
      duration: 2000
    })
  }
}

// 选择攻略
const selectGuide = (index) => {
  selectedGuideIndex.value = index
}

// 获取攻略标题
const getGuideTitle = (guide) => {
  if (guide.startLocation && guide.endLocation && guide.totalDays) {
    return `从${guide.startLocation}到${guide.endLocation}的${guide.totalDays}天攻略`
  }
  return `攻略 - ${formatDate(guide.createTime)}`
}

// 解析Markdown - 使用新的 markdownService
const parseMarkdown = (text, guide) => {
  if (!text) return ''

  let processedText = text

  // 移除一级标题 "# Smart Travel Plan"
  processedText = processedText.replace(/^# Smart Travel Plan\s*\n*/m, '')

  // 1. 先把图片语法但url不是图片的内容，转成超链接语法
  processedText = processedText.replace(
    /!\[([^\]]*)\]\((https?:\/\/[^)]+)\)/g,
    (match, alt, url) => {
      // 如果url不是图片后缀，则转为超链接
      if (!url.match(/\.(png|jpe?g|gif|webp|svg)(\?.*)?$/i)) {
        return `[${alt}](${url})`
      }
      return match
    }
  )

  // 2. 处理本地相对路径图片，转换为绝对路径
  if (userStore.userInfo?.account) {
    processedText = processedText.replace(
      /!\[([^\]]*)\]\(\.?\/?([^)]+)\)/g,
      (match, alt, src) => {
        if (src.startsWith('http')) {
          return match
        }
        const fullUrl = `${API_BASE}/public/${userStore.userInfo.account}/${src}`
        return `![${alt}](${fullUrl})`
      }
    )
  }

  return markdownService.parse(processedText)
}

// 跳转到首页制作攻略
const goToHome = () => {
  router.go('/')
}

// 监听登录状态变化，加载攻略
onMounted(() => {
  if (userStore.isLoggedIn && activeTab.value === 'guides') {
    loadUserGuides()
  }
})

// 监听tab切换
watch(activeTab, (newTab) => {
  if (newTab === 'guides' && userStore.isLoggedIn && userGuides.value.length === 0) {
    loadUserGuides()
  }
})

// 处理头像上传
const handleAvatarChange = (file) => {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  const isImage = validTypes.includes(file.raw.type)
  const isLt10M = file.raw.size / 1024 / 1024 < 10

  if (!isImage) {
    ElMessage.error({
      message: '只能上传图片文件!',
      duration: 1000
    })
    return false
  }
  if (!isLt10M) {
    ElMessage.error({
      message: '图片大小不能超过10MB!',
      duration: 1000
    })
    return false
  }

  uploadAvatar(file.raw)
  return true
}

// 上传头像
const uploadAvatar = async (file) => {
  try {
    const result = await userStore.updateAvatar(file)
    if (result.success) {
      ElMessage.success({
        message: '头像更新成功',
        duration: 1000
      })
      userStore.userInfo.avatar = result.avatarUrl
    } else {
      ElMessage.error({
        message: result.message || '头像更新失败',
        duration: 1000
      });
    }
  } catch (error) {
    ElMessage.error({
      message: '头像上传失败: ' + (error.message || '未知错误'),
      duration: 1000
    })
  }
}

// 处理修改密码
const handleChangePassword = async () => {
  try {
    // 修正为调用表单引用的validate方法
    await passwordFormRef.value.validate()

    const result = await userStore.changePassword({
      currentPassword: passwordForm.value.currentPassword, // 明文传递
      newPassword: passwordForm.value.newPassword          // 明文传递
    })

    if (result.success) {
      ElMessage.success({ message: '密码更新成功', duration: 1000 })
      // 清除表单数据应使用表单引用的resetFields方法
      passwordFormRef.value.resetFields()
      showChangePassword.value = false
    }
  } catch (error) {
    console.error('密码修改失败:', error)
    ElMessage.error({
      message: error.response?.data?.message || '密码修改失败',
      duration: 1500
    })
  }
}

// 查看攻略详情
const viewGuide = (guide) => {
  console.log('查看攻略:', guide)
}

// 格式化日期
const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

// 图片加载错误处理
const handleImageError = (event) => {
  console.error('图片加载失败:', event.target.src)
  // 可以设置一个默认的占位图片
  event.target.style.display = 'none'
  event.target.nextElementSibling.textContent = '图片加载失败'
}

// 跳转到登录页面
const goToLogin = () => {
  router.go('/user-center/login')
}

// 注销处理
const handleLogout = () => {
  ElMessageBox.confirm('确定要注销当前登录吗？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logout();
    userStore.clearStoredPassword();
    router.go('/user-center/login');
    ElMessage.success({
      message: '已成功注销',
      duration: 2000
    })
  }).catch(() => {});
};

// 游记相关处理方法
const handleJournalCreated = (result) => {
  console.log('游记创建成功:', result)
  ElMessage.success('游记创建成功')
}

const handleJournalUpdated = (result) => {
  console.log('游记更新成功:', result)
  ElMessage.success('游记更新成功')
}

const handleJournalDeleted = (journalId) => {
  console.log('游记删除成功:', journalId)
  ElMessage.success('游记删除成功')
}

const handleJournalClick = (journal) => {
  console.log('点击游记:', journal)
  // 这里可以打开游记详情页面或者做其他处理
}
</script>

<style scoped>
.user-profile {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.profile-card {
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.profile-header {
  position: relative;
}

.profile-cover {
  height: 200px;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  position: relative;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.4));
}

.profile-info {
  position: relative;
  padding: 0 2rem 2rem;
  margin-top: -60px;
}

.avatar-wrapper {
  position: relative;
  display: inline-block;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 4px solid #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  object-fit: cover;
}

.avatar-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  padding: 0.5rem;
  border-radius: 0 0 60px 60px;
  opacity: 0;
  transition: opacity 0.3s;
}

.avatar-wrapper:hover .avatar-actions {
  opacity: 1;
}

.upload-btn {
  width: 100%;
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.user-details {
  margin-top: 1rem;
}

.user-name {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-name h1 {
  margin: 0;
  font-size: 1.8rem;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-signature {
  margin: 0.5rem 0;
  color: #666;
  font-size: 1rem;
  position: relative;
}

.editable-field {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

/* 统一按钮间距 */
.card-header .el-button {
  margin-left: auto;
  padding: 6px 12px;
}

/* 输入框防换行 */
.editable-field :deep(.el-input__inner) {
  white-space: nowrap;
  max-width: 400px;
}

.editable-field:hover .edit-icon {
  opacity: 1;
}

.edit-icon {
  color: #409eff;
  margin-left: 8px;
  cursor: pointer;
  transition: opacity 0.3s;
  opacity: 0;
}

.edit-icon:hover {
  color: #409eff !important;
  cursor: pointer;
}

/* 对话框按钮容器 */
.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.el-input, .el-textarea {
  width: 80%;
}

.profile-content {
  background: #fff;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 攻略容器布局 */
.guides-container {
  display: flex;
  /* 高度根据左侧10个攻略项计算：sidebar-header + guides-list */
  height: calc(60px + 10 * 80px + 1rem);
  min-height: 400px; /* 最小高度保底 */
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

/* 左侧攻略目录 */
.guides-sidebar {
  width: 300px;
  border-right: 1px solid #e4e7ed;
  background: #fafafa;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid #e4e7ed;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}

.guide-count {
  font-size: 0.9rem;
  color: #666;
}

.guides-list {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
  /* 计算10个攻略项的高度：每个item (padding 2rem + content ~40px + margin 0.5rem) = ~80px */
  max-height: calc(10 * 80px + 1rem); /* 10个攻略项的高度 + guides-list的padding */
}

.guide-item {
  padding: 1rem;
  margin-bottom: 0.5rem;
  background: #fff;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.guide-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.guide-item.active {
  border-color: #409eff;
  background: #ecf5ff;
}

.guide-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.guide-item-header h4 {
  margin: 0;
  font-size: 0.95rem;
  color: #333;
  line-height: 1.4;
  flex: 1;
  margin-right: 0.5rem;
}

.guide-days {
  background: #409eff;
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  white-space: nowrap;
}

.guide-item-meta {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  color: #666;
}

.guide-date {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.empty-guides {
  padding: 2rem;
  text-align: center;
}

/* 右侧攻略详情 */
.guides-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.guide-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.guide-detail-header {
  padding: 1rem;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.guide-detail-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
  color: #333;
}

.guide-detail-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #666;
}

/* 攻略内容滚动区域 */
.guide-scroll-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  /* 与左侧目录高度保持一致：
     左侧总高度 = sidebar-header(~60px) + guides-list(10*80px + 1rem)
     右侧内容高度 = 左侧总高度 - guide-detail-header(~80px) */
  height: calc(60px + 10 * 80px + 1rem - 80px);
  max-height: calc(60px + 10 * 80px + 1rem - 80px);
}

.no-guide-selected {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 攻略内容样式 - 参考MapContainer */
.guide-scroll-container .answer-area {
  white-space: pre-wrap;
  max-width: 100%;
  font-family: var(--vp-font-family-base);
  line-height: 1.7;
  transition: border-color 0.25s, background-color 0.25s, box-shadow 0.25s;
  word-break: break-word;
  tab-size: 2;
}

.guide-scroll-container .answer-area a {
  color: #1890ff;
  font-weight: bold;
  border-bottom: 1.5px dashed #1890ff;
  text-decoration: none;
  transition: color 0.2s, border-bottom 0.2s;
  cursor: pointer;
  padding-right: 4px;
  position: relative;
}

.guide-scroll-container .answer-area a::after {
  content: '🔗';
  font-size: 0.95em;
  margin-left: 2px;
  vertical-align: middle;
  opacity: 0.7;
}

.guide-scroll-container .answer-area a:hover {
  color: #ff7a45;
  border-bottom: 2px solid #ff7a45;
  text-decoration: underline;
}

.guide-scroll-container .answer-area-container {
  position: relative;
  margin-bottom: 1rem;
  padding: 1.5rem;
  background: var(--vp-c-bg-soft);
  border-radius: 12px;
  border: 1px solid var(--vp-c-divider-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.guide-scroll-container .answer-area-container:hover {
  border-color: var(--vp-c-brand-soft);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.guide-scroll-container .planning-box {
  margin: 1rem 0;
  text-align: center;
}

.guide-scroll-container .map-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.guide-scroll-container .vitepress-divider {
  border: 0;
  height: 1px;
  background: var(--vp-c-divider);
  margin: 32px auto;
  position: relative;
  width: 85%;
}

.guide-scroll-container .vitepress-divider::after {
  content: '';
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg,
          transparent 0%,
          var(--vp-c-brand) 50%,
          transparent 100%);
  top: -1px;
}

/* 路线规划图片样式 */
.map-images-container {
  margin: 1rem 0;
}

.map-image-wrapper {
  margin: 1rem 0;
  text-align: center;
}

.map-image {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--vp-c-divider-light);
  transition: all 0.3s ease;
}

.map-image:hover {
  transform: scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.map-caption {
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: var(--vp-c-text-2);
  font-style: italic;
}

.security-settings {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (max-width: 768px) {
  .user-profile {
    padding: 1rem;
  }

  .profile-info {
    padding: 0 1rem 1rem;
  }

  .guides-container {
    flex-direction: column;
    height: auto;
  }

  .guides-sidebar {
    width: 100%;
    max-height: 300px;
  }

  .guides-content {
    min-height: 400px;
  }

  .guide-detail-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
}

.login-prompt {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 2rem;
}

.login-prompt :deep(.el-alert) {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 1.5rem;
  text-align: center;
}

.login-prompt :deep(.el-alert__content) {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  width: 100%;
}

@media (max-width: 768px) {
  .login-prompt {
    padding: 0 1rem;
  }
}
</style>