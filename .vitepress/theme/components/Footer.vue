<template>
  <footer class="footer">
    <div class="footer-content">
      <div class="footer-section">
        <p>© 2025 重庆攀达曼互联网科技有限公司. All rights reserved.</p>
        <p>ICP备案号: 蜀ICP备2025146034号</p>
      </div>
      <div class="footer-section">
        <a href="/privacy">隐私政策</a> |
        <a href="/terms">使用条款</a> |
        <a href="/contact">联系我们</a>
      </div>
    </div>
  </footer>
</template>

<script setup>
// 组件逻辑
</script>

<style scoped>
.footer {
  padding: 20px;
  background-color: var(--vp-c-bg-alt);
  text-align: center;
  margin-top: 40px;
  border-top: 1px solid var(--vp-c-border);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
}

.footer-section {
  margin: 10px 0;
  color: var(--vp-c-text-2);
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer a {
  color: var(--vp-c-text-2);
  text-decoration: none;
  margin: 0 10px;
  transition: color 0.3s ease;
  position: relative;
}

.footer a:hover {
  color: var(--vp-c-brand);
  text-decoration: underline;
}

/* 暗色主题适配 */
.dark .footer {
  background-color: var(--vp-c-bg-alt);
  border-top: 1px solid var(--vp-c-border);
}

.dark .footer-section {
  color: var(--vp-c-text-2);
}

.dark .footer a {
  color: var(--vp-c-text-2);
}

.dark .footer a:hover {
  color: var(--vp-c-brand-light);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .footer {
    padding: 15px;
    margin-top: 30px;
  }

  .footer-section {
    font-size: 13px;
    margin: 8px 0;
  }

  .footer a {
    margin: 0 5px;
    display: inline-block;
    padding: 2px 0;
  }
}

/* 增强视觉效果 */
.footer-section:first-child {
  font-weight: 500;
}

.footer-section p {
  margin: 5px 0;
  line-height: 1.5;
}

/* 添加微妙的渐变效果 */
.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--vp-c-brand-soft) 50%, transparent 100%);
}

.footer {
  position: relative;
}
</style>