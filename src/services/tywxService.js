// 本文件提供阿里通义万相 AI API 服务
const logger = require('../log/logger');
const fetch = require("node-fetch");
const fs = require('fs');
const path = require('path');

const TYWX_AI_IMG_TASK_URL = process.env.TYWX_AI_IMG_TASK_URL;
const TYWX_AI_IMG_FETCH_URL = process.env.TYWX_AI_IMG_FETCH_URL;
const TYWX_AI_IMG_ACCESS_KEY = process.env.TYWX_AI_IMG_ACCESS_KEY;

const VITE_BACKEND_SRV_URL = process.env.VITE_BACKEND_SRV_URL;

function checkImageExists(type, location, name) {
    const imgPath = path.join(__dirname, '..', '..', 'public', 'images', type, `${location}_${name}.png`);
    console.log(`imgPath: ${imgPath}`);
    return fs.existsSync(imgPath);
}

async function createImgGenTask(type, location, name, prompt) {
    // 首先尝试从本地查询是否存在图片
    try {
        const exists = checkImageExists(type, location, name);
        if (exists) {
            logger.info(`本地存在此图片，直接返回`);
            return {success: true, url: `${VITE_BACKEND_SRV_URL}/images/${type}/${location}_${name}.png`};
        }
    } catch (error) {
        logger.info(`本地无此图片，开始生成`);
    }

    logger.info(`本地无此图片，开始生成`);

    let tywx_img_url = '';

    try {
        const fullUrl = TYWX_AI_IMG_TASK_URL;

        const response = await fetch(fullUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-DashScope-Async' : 'enable',
                'Authorization': `Bearer ${TYWX_AI_IMG_ACCESS_KEY}`,
            },
            body: JSON.stringify({
                "model": "wanx2.1-t2i-turbo",
                "input": {
                    "prompt": prompt
                },
                "parameters": {
                    "size": "800*600",
                    "n": 1
                }
            })
        });

        // 检查响应状态
        if (!response.ok) {
            const errorText = await response.text();
            logger.error(`请求失败, 状态码: ${response.status}, 错误信息: ${errorText}`);
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }

        // 解析响应数据
        const data = await response.json();
        const task_id = data.output.task_id;
        logger.info(`通义万相生成图片任务已创建,task_id:${task_id}`);

        // 轮询等待
        const imgApiUrl = `${TYWX_AI_IMG_FETCH_URL}${task_id}`;
        // 最大重试次数
        const maxRetries = 30;
        let retryCount = 0;

        while (retryCount++ < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 2000));
            const response = await fetch(imgApiUrl, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${TYWX_AI_IMG_ACCESS_KEY}`,
                }
            })

            if (!response.ok) {
                const errorText = await response.text();
                logger.error(`请求失败, 状态码: ${response.status}, 错误信息: ${errorText}`);
                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
            }

            const subData = await response.json();
            const status = subData.output.task_status;
            if (status === 'SUCCEEDED') {
                logger.info(`图片生成成功，prompt: ${prompt}，url: ${subData.output.results[0].url}`);
                tywx_img_url = subData.output.results[0].url;
                break;
            }
        }
    } catch (error) {
        logger.error(`通义万相生成图片失败: ${error.message}`);
        return {success: false, url: ''};
    }

    try {
        // 检查本地目录是否存在，如果不存在则创建
        const imgDir = path.join(__dirname, '..', '..', 'public', 'images', type);
        if (!fs.existsSync(imgDir)) {
            fs.mkdirSync(imgDir, { recursive: true });
        }

        // 开始下载图片
        const imgUrl = tywx_img_url;
        const imgResponse = await fetch(imgUrl);
        const imgBuffer = await imgResponse.arrayBuffer();
        const imgPath = path.join(__dirname, '..', '..', 'public', 'images', type, `${location}_${name}.png`);
        fs.writeFileSync(imgPath, Buffer.from(imgBuffer));
        logger.info(`图片下载成功，路径: ${imgPath}`);
    } catch (error) {
        logger.error(`下载图片失败: ${error.message}`);
        return {success: false, url: ''};
    }

    // 这里就直接返回本地图片路径
    return {success: true, url: `${VITE_BACKEND_SRV_URL}/images/${type}/${location}_${name}.png`};
}

exports.fetchAIImg = async (req, res) => {
    const { type, location, name, prompt } = req.body;

    if (!type || !location || !name || !prompt) {
        logger.error(`缺少参数：${req.body}`);
        return res.status(400).json({ error: '缺少必要的参数' });
    }

    try {
        const result = await createImgGenTask(type, location, name, `${prompt}。生成的越真实越接近照片越好。`);
        return res.json(result);
    } catch (error) {
        logger.error(`处理请求失败: ${error.message}`);
        return res.json({success: false, url: ''});
    }
}
