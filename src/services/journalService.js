const mysql = require('mysql2/promise');
const logger = require('../log/logger');
const { pool } = require('./userApi');

// 标准化地名，用于匹配相似目的地
function normalizeDestination(destination) {
    return destination
        .replace(/[市县区]/g, '') // 去掉行政区划
        .replace(/[省]/g, '') // 去掉省份
        .replace(/\s+/g, '') // 去掉空格
        .toLowerCase(); // 转小写
}

// 创建游记
async function createJournal(journalData) {
    try {
        const {
            userId,
            title,
            content,
            coverImage,
            destination,
            startLocation,
            travelDays,
            travelMode,
            travelDate,
            isPublic,
            tags,
            images
        } = journalData;

        // 验证必要字段
        if (!userId || !title || !content || !destination || !startLocation || !travelDate) {
            return { success: false, message: '缺少必要字段' };
        }

        const connection = await pool.getConnection();
        try {
            await connection.beginTransaction();

            // 插入游记主表
            const [result] = await connection.execute(
                `INSERT INTO user_journals (
                    user_id, title, content, cover_image, destination, start_location,
                    travel_days, travel_mode, travel_date, is_public, tags, images
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    userId,
                    title,
                    content,
                    coverImage,
                    destination,
                    startLocation,
                    travelDays,
                    travelMode,
                    travelDate,
                    isPublic ? 1 : 0,
                    tags ? JSON.stringify(tags) : null,
                    images ? JSON.stringify(images) : null
                ]
            );

            const journalId = result.insertId;

            // 插入目的地索引表
            const normalizedDestination = normalizeDestination(destination);
            await connection.execute(
                `INSERT INTO journal_destinations (journal_id, destination, normalized_destination)
                 VALUES (?, ?, ?)`,
                [journalId, destination, normalizedDestination]
            );

            await connection.commit();

            logger.info('游记创建成功:', {
                journalId,
                userId,
                title,
                destination
            });

            return { success: true, journalId, message: '游记创建成功' };
        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    } catch (error) {
        logger.error('创建游记失败:', error);
        return { success: false, message: '创建游记失败' };
    }
}

// 获取用户游记列表
async function getUserJournals(userId, page = 1, pageSize = 10) {
    try {
        const offset = (page - 1) * pageSize;

        const [journals] = await pool.execute(
            `SELECT j.*, u.nickname, u.avatar
             FROM user_journals j
             JOIN users u ON j.user_id = u.id
             WHERE j.user_id = ?
             ORDER BY j.created_at DESC
             LIMIT ? OFFSET ?`,
            [userId, pageSize, offset]
        );

        const [countResult] = await pool.execute(
            'SELECT COUNT(*) as total FROM user_journals WHERE user_id = ?',
            [userId]
        );

        // 处理JSON字段
        const processedJournals = journals.map(journal => ({
            ...journal,
            tags: journal.tags ? JSON.parse(journal.tags) : null,
            images: journal.images ? JSON.parse(journal.images) : null,
            is_public: Boolean(journal.is_public)
        }));

        return {
            success: true,
            data: processedJournals,
            pagination: {
                page,
                pageSize,
                total: countResult[0].total,
                totalPages: Math.ceil(countResult[0].total / pageSize)
            }
        };
    } catch (error) {
        logger.error('获取用户游记失败:', error);
        return { success: false, message: '获取游记失败' };
    }
}

// 获取公开游记列表
async function getPublicJournals(page = 1, pageSize = 10) {
    try {
        const offset = (page - 1) * pageSize;

        const [journals] = await pool.execute(
            `SELECT j.*, u.nickname, u.avatar
             FROM user_journals j
             JOIN users u ON j.user_id = u.id
             WHERE j.is_public = 1
             ORDER BY j.created_at DESC
             LIMIT ? OFFSET ?`,
            [pageSize, offset]
        );

        const [countResult] = await pool.execute(
            'SELECT COUNT(*) as total FROM user_journals WHERE is_public = 1'
        );

        // 处理JSON字段
        const processedJournals = journals.map(journal => ({
            ...journal,
            tags: journal.tags ? JSON.parse(journal.tags) : null,
            images: journal.images ? JSON.parse(journal.images) : null,
            is_public: Boolean(journal.is_public)
        }));

        return {
            success: true,
            data: processedJournals,
            pagination: {
                page,
                pageSize,
                total: countResult[0].total,
                totalPages: Math.ceil(countResult[0].total / pageSize)
            }
        };
    } catch (error) {
        logger.error('获取公开游记失败:', error);
        return { success: false, message: '获取公开游记失败' };
    }
}

// 根据目的地获取推荐游记
async function getJournalsByDestination(destination, currentUserId, page = 1, pageSize = 5) {
    try {
        const normalizedDestination = normalizeDestination(destination);
        const offset = (page - 1) * pageSize;

        const [journals] = await pool.execute(
            `SELECT j.*, u.nickname, u.avatar
             FROM user_journals j
             JOIN users u ON j.user_id = u.id
             JOIN journal_destinations jd ON j.id = jd.journal_id
             WHERE jd.normalized_destination = ?
             AND j.is_public = 1
             AND j.user_id != ?
             ORDER BY j.view_count DESC, j.like_count DESC, j.created_at DESC
             LIMIT ? OFFSET ?`,
            [normalizedDestination, currentUserId, pageSize, offset]
        );

        // 处理JSON字段
        const processedJournals = journals.map(journal => ({
            ...journal,
            tags: journal.tags ? JSON.parse(journal.tags) : null,
            images: journal.images ? JSON.parse(journal.images) : null,
            is_public: Boolean(journal.is_public)
        }));

        return {
            success: true,
            data: processedJournals,
            message: `找到 ${journals.length} 条相关游记`
        };
    } catch (error) {
        logger.error('根据目的地获取游记失败:', error);
        return { success: false, message: '获取推荐游记失败' };
    }
}

// 获取单个游记详情
async function getJournalById(journalId, currentUserId) {
    try {
        const [journals] = await pool.execute(
            `SELECT j.*, u.nickname, u.avatar
             FROM user_journals j
             JOIN users u ON j.user_id = u.id
             WHERE j.id = ?`,
            [journalId]
        );

        if (journals.length === 0) {
            return { success: false, message: '游记不存在' };
        }

        const journal = journals[0];

        // 检查权限
        if (!journal.is_public && journal.user_id !== currentUserId) {
            return { success: false, message: '无权限查看此游记' };
        }

        // 增加浏览次数
        await pool.execute(
            'UPDATE user_journals SET view_count = view_count + 1 WHERE id = ?',
            [journalId]
        );

        // 检查当前用户是否点赞过
        let isLiked = false;
        if (currentUserId) {
            const [likeResult] = await pool.execute(
                'SELECT id FROM journal_likes WHERE journal_id = ? AND user_id = ?',
                [journalId, currentUserId]
            );
            isLiked = likeResult.length > 0;
        }

        return {
            success: true,
            data: {
                ...journal,
                tags: journal.tags ? JSON.parse(journal.tags) : null,
                images: journal.images ? JSON.parse(journal.images) : null,
                is_public: Boolean(journal.is_public),
                is_liked: isLiked,
                view_count: journal.view_count + 1 // 更新后的浏览次数
            }
        };
    } catch (error) {
        logger.error('获取游记详情失败:', error);
        return { success: false, message: '获取游记详情失败' };
    }
}

// 更新游记
async function updateJournal(journalId, userId, updateData) {
    try {
        // 检查游记是否存在且属于当前用户
        const [existing] = await pool.execute(
            'SELECT id FROM user_journals WHERE id = ? AND user_id = ?',
            [journalId, userId]
        );

        if (existing.length === 0) {
            return { success: false, message: '游记不存在或无权限编辑' };
        }

        const {
            title,
            content,
            coverImage,
            destination,
            startLocation,
            travelDays,
            travelMode,
            travelDate,
            isPublic,
            tags,
            images
        } = updateData;

        const connection = await pool.getConnection();
        try {
            await connection.beginTransaction();

            // 更新游记主表
            await connection.execute(
                `UPDATE user_journals SET
                    title = ?, content = ?, cover_image = ?, destination = ?,
                    start_location = ?, travel_days = ?, travel_mode = ?,
                    travel_date = ?, is_public = ?, tags = ?, images = ?
                 WHERE id = ? AND user_id = ?`,
                [
                    title,
                    content,
                    coverImage,
                    destination,
                    startLocation,
                    travelDays,
                    travelMode,
                    travelDate,
                    isPublic ? 1 : 0,
                    tags ? JSON.stringify(tags) : null,
                    images ? JSON.stringify(images) : null,
                    journalId,
                    userId
                ]
            );

            // 更新目的地索引
            if (destination) {
                const normalizedDestination = normalizeDestination(destination);
                await connection.execute(
                    `UPDATE journal_destinations SET
                        destination = ?, normalized_destination = ?
                     WHERE journal_id = ?`,
                    [destination, normalizedDestination, journalId]
                );
            }

            await connection.commit();

            logger.info('游记更新成功:', {
                journalId,
                userId,
                title
            });

            return { success: true, message: '游记更新成功' };
        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    } catch (error) {
        logger.error('更新游记失败:', error);
        return { success: false, message: '更新游记失败' };
    }
}

// 删除游记
async function deleteJournal(journalId, userId) {
    try {
        const [result] = await pool.execute(
            'DELETE FROM user_journals WHERE id = ? AND user_id = ?',
            [journalId, userId]
        );

        if (result.affectedRows === 0) {
            return { success: false, message: '游记不存在或无权限删除' };
        }

        logger.info('游记删除成功:', {
            journalId,
            userId
        });

        return { success: true, message: '游记删除成功' };
    } catch (error) {
        logger.error('删除游记失败:', error);
        return { success: false, message: '删除游记失败' };
    }
}

// 切换游记公开状态
async function toggleJournalPublic(journalId, userId) {
    try {
        const [result] = await pool.execute(
            'UPDATE user_journals SET is_public = NOT is_public WHERE id = ? AND user_id = ?',
            [journalId, userId]
        );

        if (result.affectedRows === 0) {
            return { success: false, message: '游记不存在或无权限操作' };
        }

        // 获取更新后的状态
        const [updated] = await pool.execute(
            'SELECT is_public FROM user_journals WHERE id = ?',
            [journalId]
        );

        const isPublic = Boolean(updated[0].is_public);

        logger.info('游记公开状态切换成功:', {
            journalId,
            userId,
            isPublic
        });

        return {
            success: true,
            isPublic,
            message: `游记已${isPublic ? '设为公开' : '设为私有'}`
        };
    } catch (error) {
        logger.error('切换游记公开状态失败:', error);
        return { success: false, message: '操作失败' };
    }
}

// 点赞/取消点赞游记
async function toggleJournalLike(journalId, userId) {
    try {
        const connection = await pool.getConnection();
        try {
            await connection.beginTransaction();

            // 检查是否已点赞
            const [existing] = await connection.execute(
                'SELECT id FROM journal_likes WHERE journal_id = ? AND user_id = ?',
                [journalId, userId]
            );

            let isLiked = false;
            if (existing.length > 0) {
                // 取消点赞
                await connection.execute(
                    'DELETE FROM journal_likes WHERE journal_id = ? AND user_id = ?',
                    [journalId, userId]
                );
                isLiked = false;
            } else {
                // 点赞
                await connection.execute(
                    'INSERT INTO journal_likes (journal_id, user_id) VALUES (?, ?)',
                    [journalId, userId]
                );
                isLiked = true;
            }

            await connection.commit();

            logger.info('游记点赞状态切换成功:', {
                journalId,
                userId,
                isLiked
            });

            return {
                success: true,
                isLiked,
                message: isLiked ? '点赞成功' : '取消点赞成功'
            };
        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    } catch (error) {
        logger.error('切换游记点赞状态失败:', error);
        return { success: false, message: '操作失败' };
    }
}

// 添加游记评论
async function addJournalComment(journalId, userId, content, parentId = null) {
    try {
        const [result] = await pool.execute(
            'INSERT INTO journal_comments (journal_id, user_id, content, parent_id) VALUES (?, ?, ?, ?)',
            [journalId, userId, content, parentId]
        );

        const commentId = result.insertId;

        logger.info('游记评论添加成功:', {
            commentId,
            journalId,
            userId,
            parentId
        });

        return {
            success: true,
            commentId,
            message: '评论添加成功'
        };
    } catch (error) {
        logger.error('添加游记评论失败:', error);
        return { success: false, message: '评论失败' };
    }
}

// 获取游记评论
async function getJournalComments(journalId, page = 1, pageSize = 10) {
    try {
        const offset = (page - 1) * pageSize;

        const [comments] = await pool.execute(
            `SELECT c.*, u.nickname, u.avatar
             FROM journal_comments c
             JOIN users u ON c.user_id = u.id
             WHERE c.journal_id = ?
             ORDER BY c.created_at DESC
             LIMIT ? OFFSET ?`,
            [journalId, pageSize, offset]
        );

        const [countResult] = await pool.execute(
            'SELECT COUNT(*) as total FROM journal_comments WHERE journal_id = ?',
            [journalId]
        );

        return {
            success: true,
            data: comments,
            pagination: {
                page,
                pageSize,
                total: countResult[0].total,
                totalPages: Math.ceil(countResult[0].total / pageSize)
            }
        };
    } catch (error) {
        logger.error('获取游记评论失败:', error);
        return { success: false, message: '获取评论失败' };
    }
}

module.exports = {
    createJournal,
    getUserJournals,
    getPublicJournals,
    getJournalsByDestination,
    getJournalById,
    updateJournal,
    deleteJournal,
    toggleJournalPublic,
    toggleJournalLike,
    addJournalComment,
    getJournalComments
};